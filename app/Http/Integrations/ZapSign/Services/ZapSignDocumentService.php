<?php

namespace App\Http\Integrations\ZapSign\Services;

use App\Http\Integrations\ZapSign\DataTransferObjects\ZapSignCreateDocumentFromPdfUploadDto;
use App\Http\Integrations\ZapSign\Requests\Document\ZapSignCreateDocumentFromPdfUploadRequest;
use App\Http\Integrations\ZapSign\Requests\Document\ZapSignGetDocumentByTokenRequest;
use App\Http\Integrations\ZapSign\ZapSignConnector;

class ZapSignDocumentService extends ZapSignBaseService
{
    public function getByToken(string $token): ?object
    {
        $connector = new ZapSignConnector($this->token);
        $request = new ZapSignGetDocumentByTokenRequest($token);
        $response = $connector->send($request);

        return json_decode($response->body());
    }

    public function createDocumentFromPdfUpload(ZapSignCreateDocumentFromPdfUploadDto $zapSignCreateDocumentFromPdfUploadDto): mixed
    {
        $connector = new ZapSignConnector($this->token);
        $request = new ZapSignCreateDocumentFromPdfUploadRequest($zapSignCreateDocumentFromPdfUploadDto);
        $response = $connector->send($request);

        return json_decode($response->body());
    }
}

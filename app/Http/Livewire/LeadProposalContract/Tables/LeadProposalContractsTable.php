<?php

namespace App\Http\Livewire\LeadProposalContract\Tables;

use App\Core\Http\Livewire\LaravelLivewireTables\BaseLivewireTable;
use App\Core\Http\Livewire\LaravelLivewireTables\ButtonGenerator;
use App\Core\Http\Livewire\LaravelLivewireTables\ButtonLinkColumn;
use App\Models\LeadProposalContract;
use Illuminate\Database\Eloquent\Builder;
use Rappasoft\LaravelLivewireTables\Views\Column;
use Rappasoft\LaravelLivewireTables\Views\Columns\ButtonGroupColumn;

class LeadProposalContractsTable extends BaseLivewireTable
{
    protected $model = LeadProposalContract::class;
    protected string $resourceRoute = 'lead_proposal_contracts';

    public int $leadId;
    public int $leadProposalId;

    public function mount(int $leadId, int $leadProposalId): void
    {
        $this->leadId = $leadId;
        $this->leadProposalId = $leadProposalId;
    }

    public function builder(): Builder
    {
        return LeadProposalContract::query()
            ->whereRelation('leadProposal', 'lead_id', $this->leadId)
            ->orderByDesc('id');
    }

    public function columns(): array
    {
        return [
            Column::make(__('lead_proposal_contracts.forms.fields.id'), 'id')
                ->sortable()
                ->searchable(),
            Column::make(__('lead_proposal_contracts.forms.fields.lead_proposal_id'), 'lead_proposal_id')
                ->sortable()
                ->searchable(),
            Column::make('zap_sign_open_id', 'zap_sign_open_id')
                ->hideIf(true),
            ButtonGroupColumn::make('Ações')
                ->buttons([
                    ButtonLinkColumn::make('Ver')
                        ->title(fn(): string => '')
                        ->location(fn(): string => '')
                        ->renderHtml(function (LeadProposalContract $row): mixed {
                            $viewPdfButton = (new ButtonGenerator())->generateLinkButton(
                                route: route('lead_proposal_contracts.pdf', [$this->leadId, $row->lead_proposal_id, $row->id]),
                                icon: "<i class='fas fa-file-alt'></i>",
                                title: 'Ver',
                                size: 'sm',
                                primary: true,
                                outline: true,
                                customClass: 'datatable-button',
                                dataAttributes: "target='_blank'",
                            );

                            $createInZapSignButton = '';

                            if (is_null($row->zap_sign_open_id)) {
                                $createInZapSignButton = (new ButtonGenerator())->generateLinkButton(
                                    route: route('lead_proposal_contracts.create_in_zap_sign', [$this->leadId, $row->lead_proposal_id, $row->id]),
                                    icon: "<i class='fas fa-pen-alt'></i>",
                                    title: 'Enviar para assinatura',
                                    size: 'sm',
                                    primary: true,
                                    outline: true,
                                    customClass: 'datatable-button',
                                );
                            }

                            return $viewPdfButton . $createInZapSignButton;
                        }),
                ]),
        ];
    }
}

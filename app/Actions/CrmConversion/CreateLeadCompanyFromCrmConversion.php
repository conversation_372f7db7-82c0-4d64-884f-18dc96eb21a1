<?php

namespace App\Actions\CrmConversion;

use App\Models\CrmConversion;
use App\Models\LeadCompany;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\ActionRequest;
use Lorisle<PERSON>\Actions\Concerns\AsAction;
use Throwable;

class CreateLeadCompanyFromCrmConversion
{
    use AsAction;

    public function asController(ActionRequest $request, CrmConversion $crmConversion): RedirectResponse
    {
        try {
            $this->handle($crmConversion);
            return redirect_success('crm_conversions.index', __('crm_conversions.responses.create_lead_company.success'));
        } catch (Throwable $th) {
            return redirect_error($th->getMessage());
        }
    }

    public function handle(CrmConversion $crmConversion): LeadCompany
    {
        try {
            return DB::transaction(function () use ($crmConversion): LeadCompany {
                /** @var \App\Models\LeadCompany $leadCompany */
                $leadCompany = LeadCompany::create([
                    'crm_conversion_id' => $crmConversion->id,
                    'name' => $crmConversion->company_name ?: $crmConversion->name,
                    'trading_name' => $crmConversion->company_trading_name ?: $crmConversion->name,
                    'tax_id_number' => $crmConversion->company_tax_id_number,
                    'city' => $crmConversion->city,
                    'state_registration_no' => 'ISENTO',
                    'country' => 'Brasil',
                    'national_simple' => false,
                ]);

                if ($crmConversion->name || $crmConversion->email) {
                    $leadCompany->leadCompanyContacts()->create([
                        'name' => $crmConversion->name,
                        'email' => $crmConversion->email,
                        'phone' => $crmConversion->mobile_phone ?: $crmConversion->personal_phone,
                        'job_title' => $crmConversion->job_title,
                        'main' => true,
                    ]);
                }

                return $leadCompany;
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}

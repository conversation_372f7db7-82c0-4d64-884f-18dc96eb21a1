<?php

namespace App\Actions\Lead;

use App\Http\Integrations\ZapSign\Services\ZapSignDocumentService;
use App\Models\City;
use App\Models\CrmFunnel;
use App\Models\Lead;
use App\Models\LeadBranch;
use App\Models\LeadBranchItem;
use App\Models\LeadItem;
use App\Models\LeadProposalContract;
use App\Models\Permission;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Symfony\Component\HttpFoundation\Request;
use Throwable;

class EditLead
{
    use AsAction;

    protected Lead $lead;

    protected array $rules = [
        'lead_company_id' => 'required',
        'lead_company_contact_id' => 'required',
        'lead_items' => 'nullable',
        'bid' => 'required',
        'lead_branches.id' => 'nullable',
        'lead_branches.company_name' => 'required',
        'lead_branches.company_trading_name' => 'nullable',
        'lead_branches.company_tax_id_number' => 'nullable',
        'lead_branches.branch_name' => 'required',
        'lead_branches.employee_count' => 'required',
        'lead_branches.address_zipcode' => 'nullable',
        'lead_branches.address_address' => 'nullable',
        'lead_branches.address_number' => 'nullable',
        'lead_branches.address_additional_info' => 'nullable',
        'lead_branches.address_district' => 'nullable',
        'lead_branches.address_city_id' => 'required',
        'lead_branches.address_city_name' => 'required',
        'lead_branches.address_state_id' => 'nullable',
        'lead_branches.address_state_abbreviation' => 'nullable',
        'lead_branches.cnae_id' => 'required',
        'lead_branches.procedures' => 'required|array',
    ];

    protected array $messages = [
        'name.required' => 'É obrigatório informar a razão social.',
        'trading_name.required' => 'É obrigatório informar o nome fantasia.',
        'state_registration_no.required' => 'É obrigatório informar a inscrição estadual (ou se é isento).',
        'zipcode.required' => 'É obrigatório informar o CEP.',
        'address.required' => 'É obrigatório informar o endereço.',
        'number.required' => 'É obrigatório informar o número.',
        'district.required' => 'É obrigatório informar o bairro.',
        'city.required' => 'É obrigatório informar a cidade.',
        'state.required' => 'É obrigatório informar o estado.',
        'country.required' => 'É obrigatório informar o país.',
    ];

    public function authorize(ActionRequest $request): bool
    {
        return $request->user()->can(Permission::UPDATE_LEADS);
    }

    public function asController(ActionRequest $request, Lead $lead): mixed
    {
        if ($request->method() === Request::METHOD_GET) {
            /** @var \App\Models\LeadProposalContract $signedLeadProposalContract */
            $signedLeadProposalContract = LeadProposalContract::query()
                ->whereRelation('leadProposal', 'lead_id', $lead->id)
                ->whereNotNull('zap_sign_signed_file_url')
                ->first();

            if ($signedLeadProposalContract) {
                $signedLeadProposalContractLink = (new ZapSignDocumentService())
                    ->getByToken($signedLeadProposalContract->zap_sign_token)
                    ->signed_file;

                $signedLeadProposalContract->update(['zap_sign_signed_file_url' => $signedLeadProposalContractLink]);
            } else {
                $signedLeadProposalContractLink = null;
            }

            return $lead->getBackendActionGeneratorInstance()->loadEditView([
                'lead' => $lead,
                'signedLeadProposalContractLink' => $signedLeadProposalContractLink,
                'crmFunnels' => CrmFunnel::query()
                    ->orderBy('name')
                    ->get(),
            ]);
        }

        $request->merge(['bid' => $request->has('bid')]);

        $validator = Validator::make($request->all(), $this->rules, $this->messages);
        $validator->validate();

        try {
            $this->handle($lead, $validator->validated());
            return redirect_success('leads.index', __('leads.responses.update.success'));
        } catch (Throwable $th) {
            return redirect_error($th->getMessage());
        }
    }

    public function handle(Lead $lead, array $data): Lead
    {
        $this->lead = $lead;

        try {
            return DB::transaction(function () use ($data): Lead {
                $this->lead->update($data);

                $this->updateLeadItems($data);
                $this->updateLeadBranches($data);

                return $this->lead;
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }

    protected function updateLeadItems(array $data): void
    {
        if (!isset($data['lead_items'])) {
            $this->lead->leadItems()->delete();
            return;
        }

        $leadItemsData = $data['lead_items'];

        $this->lead->leadItems()
            ->whereNotIn('id', collect($leadItemsData)->pluck('lead_proposal_item_id')->toArray())
            ->delete();

        foreach ($leadItemsData as $leadItemData) {
            if (!isset($leadItemData['lead_proposal_item_quantity']) || is_null($leadItemData['lead_proposal_item_quantity'])) {
                $leadItemData['lead_proposal_item_quantity'] = 1;
            }

            if ((int) $leadItemData['lead_proposal_item_id'] === 0) {
                $this->lead->leadItems()->create([
                    'procedure_id' => $leadItemData['lead_proposal_item_procedure_id'],
                    'type' => $leadItemData['lead_proposal_item_type'],
                    'quantity' => $leadItemData['lead_proposal_item_quantity'] ?? 1,
                    'minimum_amount' => $leadItemData['lead_proposal_item_minimum_amount'],
                    'unit_amount' => $leadItemData['lead_proposal_item_unit_amount'],
                    'discount_percentage' => $leadItemData['lead_proposal_item_discount_percentage'],
                    'discount_amount' => $leadItemData['lead_proposal_item_discount_amount'],
                    'addition_percentage' => $leadItemData['lead_proposal_item_addition_percentage'],
                    'addition_amount' => $leadItemData['lead_proposal_item_addition_amount'],
                    'total_amount' => $leadItemData['lead_proposal_item_total_amount'],
                ]);

                continue;
            }

            LeadItem::find($leadItemData['lead_proposal_item_id'])->update([
                'procedure_id' => $leadItemData['lead_proposal_item_procedure_id'],
                'type' => $leadItemData['lead_proposal_item_type'],
                'quantity' => $leadItemData['lead_proposal_item_quantity'] ?? 1,
                'minimum_amount' => $leadItemData['lead_proposal_item_minimum_amount'],
                'unit_amount' => $leadItemData['lead_proposal_item_unit_amount'],
                'discount_percentage' => $leadItemData['lead_proposal_item_discount_percentage'],
                'discount_amount' => $leadItemData['lead_proposal_item_discount_amount'],
                'addition_percentage' => $leadItemData['lead_proposal_item_addition_percentage'],
                'addition_amount' => $leadItemData['lead_proposal_item_addition_amount'],
                'total_amount' => $leadItemData['lead_proposal_item_total_amount'],
            ]);
        }
    }

    protected function updateLeadBranches(array $data): void
    {
        if (!isset($data['lead_branches'])) {
            $this->lead->leadBranches()->delete();
            return;
        }

        $leadBranchesData = $data['lead_branches'];

        $leadBranchIds = collect($leadBranchesData)
            ->pluck('id')
            ->filter(fn(int $value): bool => $value > 0)
            ->toArray();

        $this->lead->leadBranches()
            ->whereNotIn('id', $leadBranchIds)
            ->get()
            ->each(function (LeadBranch $leadBranch): void {
                $leadBranch->leadBranchItems()->delete();
                $leadBranch->delete();
            });

        foreach ($leadBranchesData as $leadBranchData) {
            $this->processSingleLeadBranch($leadBranchData);
        }
    }

    protected function processSingleLeadBranch(array $leadBranchData): void
    {
        if (!isset($leadBranchData['procedures']) || count($leadBranchData['procedures']) === 0) {
            return;
        }

        if ((int) $leadBranchData['id'] === 0) {
            $this->createSingleLeadBranch($leadBranchData);
            return;
        }

        /** @var \App\Models\LeadBranch $leadBranch */
        $leadBranch = LeadBranch::find($leadBranchData['id']);

        $this->updateSingleLeadBranch($leadBranch, $leadBranchData);
    }

    protected function createSingleLeadBranch(array $leadBranchData): void
    {
        /** @var \App\Models\City $city */
        $city = City::find($leadBranchData['address_city_id']);

        /** @var \App\Models\LeadBranch $leadBranch */
        $leadBranch = $this->lead->leadBranches()->create([
            'cnae_id' => $this->lead->cnae_id,
            'name' => $leadBranchData['company_name'],
            'trading_name' => $leadBranchData['company_trading_name'],
            'tax_id_number' => $leadBranchData['company_tax_id_number'],
            'branch_name' => $leadBranchData['branch_name'],
            'employee_count' => $leadBranchData['employee_count'],
            'state_registration_no' => $leadBranchData['state_registration_no'] ?? null,
            'city_registration_no' => $leadBranchData['city_registration_no'] ?? null,
            'zipcode' => $leadBranchData['address_zipcode'],
            'address' => $leadBranchData['address_address'],
            'number' => $leadBranchData['address_number'],
            'additional_info' => $leadBranchData['address_additional_info'],
            'district' => $leadBranchData['address_district'],
            'city_id' => $city->id,
            'city' => $city->name,
            'state_id' => $city->state_id,
            'state' => $city->state->abbreviation,
            'country' => 'BR',
            'national_simple' => false,
        ]);

        foreach ($leadBranchData['procedures'] as $procedure) {
            $leadBranch->leadBranchItems()->create([
                'procedure_id' => $procedure['lead_proposal_company_item_procedure_id'],
                'type' => $procedure['lead_proposal_company_item_type'],
                'quantity' => $procedure['lead_proposal_company_item_quantity'],
                'unit_amount' => $procedure['lead_proposal_company_item_unit_amount'],
                'discount_percentage' => $procedure['lead_proposal_company_item_discount_percentage'],
                'discount_amount' => $procedure['lead_proposal_company_item_discount_amount'],
                'addition_percentage' => $procedure['lead_proposal_company_item_addition_percentage'],
                'addition_amount' => $procedure['lead_proposal_company_item_addition_amount'],
                'total_amount' => $procedure['lead_proposal_company_item_total_amount'],
            ]);
        }
    }

    protected function updateSingleLeadBranch(LeadBranch $leadBranch, array $leadBranchData): void
    {
        /** @var \App\Models\City $city */
        $city = City::find($leadBranchData['address_city_id']);

        $leadBranch->update([
            'cnae_id' => $this->lead->cnae_id,
            'name' => $leadBranchData['company_name'],
            'trading_name' => $leadBranchData['company_trading_name'],
            'tax_id_number' => $leadBranchData['company_tax_id_number'],
            'branch_name' => $leadBranchData['branch_name'],
            'employee_count' => $leadBranchData['employee_count'],
            'state_registration_no' => $leadBranchData['state_registration_no'] ?? null,
            'city_registration_no' => $leadBranchData['city_registration_no'] ?? null,
            'zipcode' => $leadBranchData['address_zipcode'],
            'address' => $leadBranchData['address_address'],
            'number' => $leadBranchData['address_number'],
            'additional_info' => $leadBranchData['address_additional_info'],
            'district' => $leadBranchData['address_district'],
            'city_id' => $city->id,
            'city' => $city->name,
            'state_id' => $city->state_id,
            'state' => $city->state->abbreviation,
            'country' => 'BR',
            'national_simple' => false,
        ]);

        $leadBranchItemIds = collect($leadBranchData['procedures'])
            ->pluck('lead_proposal_company_item_id')
            ->filter(fn(int $value): bool => $value > 0)
            ->toArray();

        $leadBranch->leadBranchItems()
            ->whereNotIn('id', $leadBranchItemIds)
            ->delete();

        foreach ($leadBranchData['procedures'] as $procedure) {
            $this->processSingleLeadBranchItem($leadBranch, $procedure);
        }
    }

    protected function processSingleLeadBranchItem(LeadBranch $leadBranch, array $procedure): void
    {
        if ((int) $procedure['lead_proposal_company_item_id'] === 0) {
            $leadBranch->leadBranchItems()->create([
                'procedure_id' => $procedure['lead_proposal_company_item_procedure_id'],
                'type' => $procedure['lead_proposal_company_item_type'],
                'quantity' => $procedure['lead_proposal_company_item_quantity'],
                'unit_amount' => $procedure['lead_proposal_company_item_unit_amount'],
                'discount_percentage' => $procedure['lead_proposal_company_item_discount_percentage'],
                'discount_amount' => $procedure['lead_proposal_company_item_discount_amount'],
                'addition_percentage' => $procedure['lead_proposal_company_item_addition_percentage'],
                'addition_amount' => $procedure['lead_proposal_company_item_addition_amount'],
                'total_amount' => $procedure['lead_proposal_company_item_total_amount'],
            ]);

            return;
        }

        /** @var \App\Models\LeadBranchItem $leadBranchItem */
        $leadBranchItem = LeadBranchItem::find($procedure['lead_proposal_company_item_id']);

        $leadBranchItem->update([
            'procedure_id' => $procedure['lead_proposal_company_item_procedure_id'],
            'type' => $procedure['lead_proposal_company_item_type'],
            'quantity' => $procedure['lead_proposal_company_item_quantity'],
            'unit_amount' => $procedure['lead_proposal_company_item_unit_amount'],
            'discount_percentage' => $procedure['lead_proposal_company_item_discount_percentage'],
            'discount_amount' => $procedure['lead_proposal_company_item_discount_amount'],
            'addition_percentage' => $procedure['lead_proposal_company_item_addition_percentage'],
            'addition_amount' => $procedure['lead_proposal_company_item_addition_amount'],
            'total_amount' => $procedure['lead_proposal_company_item_total_amount'],
        ]);
    }
}
